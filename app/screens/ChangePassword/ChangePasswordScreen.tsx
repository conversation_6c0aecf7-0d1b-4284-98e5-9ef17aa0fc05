import { observer } from "mobx-react-lite"
import { FC, useState } from "react"
import { View, ViewStyle, TouchableOpacity, TextStyle } from "react-native"
import { Screen, Text, TextField, Button, Icon } from "@/components"
import { AppStackScreenProps } from "@/navigators"
import { useHeader } from "@/utils/useHeader"
import { SupabaseAuthService } from "@/services/supabase/auth-service"
import { showErrorToast, showSuccessToast, showWarningToast } from "@/utils/toast"
import { useAppTheme } from "@/utils/useAppTheme"
import { translate } from "@/i18n"
import type { ThemedStyle } from "@/theme"

interface ChangePasswordScreenProps extends AppStackScreenProps<"ChangePassword"> {}

export const ChangePasswordScreen: FC<ChangePasswordScreenProps> = observer(function ChangePasswordScreen({ navigation }) {
  const { themed, theme: { colors } } = useAppTheme()
  const [password, setPassword] = useState("")
  const [loading, setLoading] = useState(false)

  // Transparent header with black back arrow only
  useHeader({
    leftContent: (
      <TouchableOpacity onPress={() => navigation.goBack()}>
        <Icon icon="caretLeft" size={24} color={colors.text} />
      </TouchableOpacity>
    ),
    backgroundColor: "transparent",
    includeSafeArea: true,
  })

  // Validation rules
  const hasMinChars = password.length >= 8
  const hasDigit = /\d/.test(password)
  const hasUppercase = /[A-Z]/.test(password)

  const allValid = hasMinChars && hasDigit && hasUppercase

  const handleSave = async () => {
    if (!allValid) {
      showWarningToast(translate("changePasswordScreen:passwordInvalid"))
      return
    }

    try {
      setLoading(true)
      await SupabaseAuthService.updateUserPassword(password)
      showSuccessToast(translate("changePasswordScreen:passwordUpdated"))
      navigation.goBack()
    } catch (error: any) {
      console.error("Password update failed", error)

      // Handle network errors specifically
      if (error?.message?.includes("Network request failed") || error?.name?.includes("AuthRetryableFetchError")) {
        showErrorToast("Network error. Please check your connection and try again.")
      } else {
        showErrorToast(error?.message ?? "Failed to update password")
      }
    } finally {
      setLoading(false)
    }
  }

  const isDisabled = loading

  return (
    <Screen
      preset="scroll"
      safeAreaEdges={["bottom"]}
      contentContainerStyle={themed($container)}
    >
      {/* Page Heading */}
      <Text tx="changePasswordScreen:title" preset="heading" style={themed($heading)} />
      <Text
        tx="changePasswordScreen:description"
        size="sm"
        style={themed($subheading)}
      />

      <TextField
        placeholderTx="changePasswordScreen:passwordPlaceholder"
        secureTextEntry
        value={password}
        onChangeText={setPassword}
        containerStyle={themed($inputContainer)}
      />

      {/* Validation rules */}
      <View style={themed($rulesContainer)}>
        {[
          { key: "changePasswordScreen:ruleMin8Chars", valid: hasMinChars },
          { key: "changePasswordScreen:ruleMinDigit", valid: hasDigit },
          { key: "changePasswordScreen:ruleMinUppercase", valid: hasUppercase },
        ].map((rule, idx) => (
          <View key={idx} style={themed($ruleRow)}>
            <Icon
              icon={rule.valid ? "check" : "x"}
              size={12}
              color={rule.valid ? colors.success ?? "green" : colors.error}
            />
            <Text
              tx={rule.key as any}
              size="sm"
              style={themed([ $ruleText, { color: rule.valid ? colors.text : colors.error } ])}
            />
          </View>
        ))}
      </View>

      <Button
        tx="changePasswordScreen:changeButton"
        onPress={handleSave}
        disabled={isDisabled}
        preset="filled"
        style={themed($saveButton)}
      />
    </Screen>
  )
})

/* ──────────────────────────────────────────────────────────
 * Styles
 * ─────────────────────────────────────────────────────────*/
const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  padding: spacing.lg,
})

const $heading: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.sm,
})

const $subheading: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.md,
})

const $inputContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.md,
})

const $rulesContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.lg,
})

const $ruleRow: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.xs,
  marginBottom: spacing.xs,
})

const $ruleText: ThemedStyle<TextStyle> = () => ({})

const $saveButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginTop: spacing.lg,
}) 