import { observer } from "mobx-react-lite"
import { FC, useCallback, useState, useEffect } from "react"
import { ViewStyle, TextStyle, View } from "react-native"
import { Button, Screen, Text } from "@/components"
import { OTPInput } from "@/components/OTPInput"
import { useAppTheme } from "@/utils/useAppTheme"
import { SupabaseAuthService } from "@/services/supabase/auth-service"
import { SupabaseProfileService } from "@/services/supabase/profile-service"
import { useStores } from "@/models"
import { showErrorToast, showSuccessToast } from "@/utils/toast"
import type { EmailVerificationScreenProps } from "./types"

export const EmailVerificationScreen: FC<EmailVerificationScreenProps> = observer(function EmailVerificationScreen({ route, navigation }) {
  const { email, password, displayName } = route.params
  const [code, setCode] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [isResendLoading, setIsResendLoading] = useState(false)
  const [resendCountdown, setResendCountdown] = useState(60) // 1 minute countdown
  const { themed } = useAppTheme()
  const {
    authenticationStore: { setAuthToken, setCurrentUserId },
    userProfileStore
  } = useStores()

  // Countdown timer for resend button
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null

    if (resendCountdown > 0) {
      interval = setInterval(() => {
        setResendCountdown((prev) => prev - 1)
      }, 1000)
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [resendCountdown])

  const handleVerify = useCallback(async () => {
    // code length check will be handled by disabled state
    try {
      setIsLoading(true)
      const { session, user } = await SupabaseAuthService.verifyEmailOtp(email, code)
      if (session?.access_token && user) {
        // set password now
        await SupabaseAuthService.updateUserPassword(password)
        if (displayName) {
          await SupabaseAuthService.updateUserProfile({ display_name: displayName })
        }
        setAuthToken(session.access_token)
        setCurrentUserId(user.id)

        // Fetch profile row and cache (similar to login flow)
        SupabaseProfileService.fetchCurrentUserProfile()
          .then((profile) => {
            if (profile) {
              userProfileStore.setProfile({
                userId: user.id,
                displayName: profile.name,
                email: profile.email,
                countryCode: profile.countryCode,
                phone: profile.phone,
                gender: profile.gender,
                dateOfBirth: profile.dateOfBirth,
                description: profile.description,
                location: profile.location
              })
            }
          })
          .catch(console.error)

        navigation.reset({ index: 0, routes: [{ name: "Main" }] })
      }
    } catch (e: any) {
      console.error("OTP verification failed", e)

      // Handle network errors specifically
      if (e?.message?.includes("Network request failed") || e?.name?.includes("AuthRetryableFetchError")) {
        showErrorToast("Network error. Please check your connection and try again.")
      } else if (e?.message?.includes("Invalid") || e?.message?.includes("expired")) {
        showErrorToast("Invalid or expired code. Please try again.")
      } else {
        showErrorToast("Verification failed. Please try again.")
      }
    } finally {
      setIsLoading(false)
    }
  }, [code, email, password, displayName])

  const handleResendCode = useCallback(async () => {
    if (resendCountdown > 0 || isResendLoading) return

    try {
      setIsResendLoading(true)
      await SupabaseAuthService.requestEmailOtp(email)
      showSuccessToast("Verification code sent!")
      setResendCountdown(60) // Reset countdown to 1 minute
    } catch (e: any) {
      console.error("Resend code failed", e)

      // Handle network errors specifically
      if (e?.message?.includes("Network request failed") || e?.name?.includes("AuthRetryableFetchError")) {
        showErrorToast("Network error. Please check your connection and try again.")
      } else {
        showErrorToast("Failed to resend code. Please try again.")
      }
    } finally {
      setIsResendLoading(false)
    }
  }, [email, resendCountdown, isResendLoading])

  return (
    <Screen
      preset="auto"
      contentContainerStyle={themed($container)}
      safeAreaEdges={["top", "bottom"]}
    >
      <Text text="Enter the 6-digit code" preset="heading" style={themed($heading)} />
      <Text text={`We've sent it to ${email}`} size="sm" style={themed($subheading)} />

      <OTPInput value={code} onChange={setCode} autoFocus length={6} />

      <View style={{ height: 24 }} />

      <Button
        text="Verify"
        onPress={handleVerify}
        disabled={isLoading || code.length !== 6}
        preset="reversed"
      />

      <Text
        text={resendCountdown > 0 ? `Resend code in ${resendCountdown}s` : "Resend code"}
        size="sm"
        weight="light"
        style={[
          { marginTop: 16, textAlign: "center" },
          themed(resendCountdown > 0 || isResendLoading ? $disabledText : $enabledText)
        ]}
        onPress={resendCountdown > 0 || isResendLoading ? undefined : handleResendCode}
      />
    </Screen>
  )
})

const $container: ViewStyle = {
  paddingHorizontal: 20,
  paddingTop: 40,
}

const $heading: TextStyle = {
  marginBottom: 4,
}

const $subheading: TextStyle = {
  marginBottom: 24,
}

const $codeField: ViewStyle = {
  marginBottom: 24,
}

const $enabledText: TextStyle = {
  color: "#007AFF", // Blue color for enabled state
}

const $disabledText: TextStyle = {
  color: "#999999", // Gray color for disabled state
}