import { useState } from "react"
import { useStores } from "@/models"
import { SupabaseAuthService } from "@/services/supabase/auth-service"
import { showSuccessToast, showErrorToast } from "@/utils/toast"

/**
 * Encapsulates state and actions for the Reset Password screen.
 */
export const useResetPasswordActions = () => {
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [attemptsCount, setAttemptsCount] = useState(0)
  const [isLoading, setIsLoading] = useState(false)

  const {
    authenticationStore: { authEmail, setAuthEmail, validationError },
  } = useStores()

  const error = isSubmitted ? validationError : ""

  /**
   * Sends a password-reset email via Supabase.
   * Returns `true` if the request was successful.
   */
  async function resetPassword(): Promise<boolean> {
    if (isLoading) return false

    setIsSubmitted(true)
    setAttemptsCount((c) => c + 1)

    if (validationError) return false

    try {
      setIsLoading(true)
      await SupabaseAuthService.resetPasswordForEmail(authEmail)
      showSuccessToast("Reset link has been sent to this email address")
      return true
    } catch (e: any) {
      console.error("Reset password failed", e)

      // Handle network errors specifically
      if (e?.message?.includes("Network request failed") || e?.name?.includes("AuthRetryableFetchError")) {
        showErrorToast("Network error. Please check your connection and try again.")
      } else {
        showErrorToast("Unable to send reset link. Please try again later.")
      }

      return false
    } finally {
      setIsLoading(false)
    }
  }

  return {
    authEmail,
    setAuthEmail,
    error,
    resetPassword,
    isLoading,
    attemptsCount,
  }
} 