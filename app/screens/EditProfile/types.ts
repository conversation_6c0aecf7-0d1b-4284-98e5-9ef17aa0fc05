export interface UserProfile {
  name: string
  email: string
  countryCode: string
  phone: string
  gender: string
  dateOfBirth: Date
  description: string
  location: string
  avatar?: string
}

export interface EditProfileContentProps {
  formData: EditProfileFormData
  errors: Record<string, string>
  onFieldChange: (field: keyof EditProfileFormData, value: string | Date) => void
  loading?: boolean
  originalEmail?: string
  isEmailVerified?: boolean
  onEmailVerify?: () => void
}

export interface EditProfileFormData extends UserProfile {
  // Add any additional form-specific fields if needed
}

export interface CountryCode {
  code: string
  label: string
  flag: string
}

export interface GenderOption {
  value: string
  label: string
}

export interface UseEditProfileFormProps {
  initialProfile?: Partial<UserProfile>
  onSave: (profile: UserProfile) => void
}

export interface UseEditProfileFormReturn {
  formData: EditProfileFormData
  isValid: boolean
  errors: Record<string, string>
  handleFieldChange: (field: keyof EditProfileFormData, value: string | Date) => void
  handleSubmit: () => void
  resetForm: () => void
}

export interface UseEditProfileActionsProps {
  onClose: () => void
  onSave: (profile: UserProfile) => void
  formData: EditProfileFormData
  isValid: boolean
}

export interface UseEditProfileActionsReturn {
  handleSave: () => void
  handleCancel: () => void
  handleImagePicker: () => void
} 