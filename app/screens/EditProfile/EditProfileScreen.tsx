import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useState, useCallback } from "react"
import { EditProfileContent } from "./components/EditProfileContent"
import { EmailVerificationSheet } from "./components/EmailVerificationSheet"
import type { UserProfile } from "./types"
import { AppStackScreenProps } from "@/navigators"
import { BottomSheetModalProvider } from "@gorhom/bottom-sheet"
import { SupabaseProfileService } from "@/services/supabase/profile-service"
import { SupabaseAuthService } from "@/services/supabase/auth-service"
import { useStores } from "@/models"
import { showErrorToast, showSuccessToast, showWarningToast } from "@/utils/toast"
import { useHeader } from "@/utils/useHeader"
import { translate } from "@/i18n"
import { useEditProfileForm } from "./hooks"

interface EditProfileScreenProps extends AppStackScreenProps<"EditProfile"> {}

export const EditProfileScreen: FC<EditProfileScreenProps> = observer(function EditProfileScreen({
  navigation,
  route,
}) {
  const { initialProfile } = route.params

  // Convert date string (if any) to Date object
  const processedInitialProfile = initialProfile ? {
    ...initialProfile,
    dateOfBirth:
      typeof initialProfile.dateOfBirth === "string"
        ? new Date(initialProfile.dateOfBirth)
        : initialProfile.dateOfBirth,
  } : undefined

  // Local state holding profile used to populate the form
  const [profile, setProfile] = useState<Partial<UserProfile> | undefined>(processedInitialProfile)
  const [loading, setLoading] = useState(false)

  // Email verification state
  const [originalEmail, setOriginalEmail] = useState<string>("")
  const [isEmailVerified, setIsEmailVerified] = useState(true)
  const [showEmailVerificationSheet, setShowEmailVerificationSheet] = useState(false)

  const { userProfileStore } = useStores()

  useEffect(() => {
    if (!processedInitialProfile) {
      SupabaseProfileService.fetchCurrentUserProfile()
        .then((data) => {
          if (data) {
            setProfile(data)
            setOriginalEmail(data.email) // Store original email for comparison
            userProfileStore.setProfile({
              userId: userProfileStore.userId,
              displayName: data.name,
              email: data.email,
              countryCode: data.countryCode,
              phone: data.phone,
              gender: data.gender,
              dateOfBirth: data.dateOfBirth,
              description: data.description,
              location: data.location,
            })
          }
        })
        .catch((e) => console.error("Failed to fetch profile:", e))
    } else if (processedInitialProfile?.email) {
      setOriginalEmail(processedInitialProfile.email)
    }
  }, [])

  // Prevent navigation when mandatory fields are missing
  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', (e) => {
      // If mandatory fields are complete, allow navigation
      if (userProfileStore.hasMandatoryFields) {
        return
      }

      // Prevent default behavior of leaving the screen
      e.preventDefault()

      // Show warning to user
      showWarningToast("Please complete all mandatory fields before leaving this screen.")
    })

    return unsubscribe
  }, [navigation, userProfileStore.hasMandatoryFields])

  // Form management hook
  const {
    formData,
    isValid,
    errors,
    handleFieldChange,
  } = useEditProfileForm({
    initialProfile: profile,
    onSave: () => {}, // We'll handle save at screen level
  })

  // Reset email verification when email changes
  useEffect(() => {
    if (originalEmail && formData.email !== originalEmail) {
      setIsEmailVerified(false)
    } else if (originalEmail && formData.email === originalEmail) {
      setIsEmailVerified(true)
    }
  }, [formData.email, originalEmail])



  const handleBack = useCallback(() => {
    // Check if mandatory fields are missing
    if (!userProfileStore.hasMandatoryFields) {
      showWarningToast("Please complete all mandatory fields before leaving this screen.")
      return
    }
    navigation.goBack()
  }, [navigation, userProfileStore.hasMandatoryFields])

  // Email verification handlers
  const handleEmailVerify = useCallback(async () => {
    try {
      await SupabaseAuthService.requestEmailChange(formData.email)
      setShowEmailVerificationSheet(true)
      showSuccessToast("Verification code sent to your new email!")
    } catch (error) {
      console.error("Error requesting email change:", error)
      showErrorToast("Failed to send verification code. Please try again.")
    }
  }, [formData.email])

  const handleEmailVerificationSuccess = useCallback(() => {
    setIsEmailVerified(true)
    setOriginalEmail(formData.email) // Update original email to the new verified email
  }, [formData.email])

  const handleSave = async () => {
    if (!isValid) {
      return
    }

    // Check if email has changed but not verified
    if (originalEmail && formData.email !== originalEmail && !isEmailVerified) {
      showWarningToast("Please verify your new email address before saving.")
      return
    }

    setLoading(true)
    try {
      // Create a copy of formData, but use original email if email changed but not verified
      const dataToSave = {
        ...formData,
        email: (originalEmail && formData.email !== originalEmail && !isEmailVerified)
          ? originalEmail
          : formData.email
      }

      await SupabaseProfileService.updateCurrentUserProfile(dataToSave)
      // update cache store
      userProfileStore.setProfile({
        userId: userProfileStore.userId,
        displayName: dataToSave.name,
        email: dataToSave.email,
        countryCode: dataToSave.countryCode,
        phone: dataToSave.phone,
        gender: dataToSave.gender,
        dateOfBirth: dataToSave.dateOfBirth,
        description: dataToSave.description,
        location: dataToSave.location,
      })
      // Show success toast *before* closing the screen so it's visible
      showSuccessToast("Profile updated successfully")
      navigation.goBack()
    } catch (error) {
      console.error("Error saving profile:", error)
      showErrorToast("Failed to save profile. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  // Configure header with back button and save button
  useHeader({
    title: translate("editProfileScreen:editProfileTitle"),
    showBackButton: true,
    onBackPress: handleBack,
    rightButtons: [
      {
        text: translate("common:save"),
        onPress: handleSave,
        style: {
          opacity: (!isValid || loading) ? 0.5 : 1,
          pointerEvents: (!isValid || loading) ? 'none' : 'auto'
        },
      },
    ],
  }, [isValid, loading, formData])

  // Wrap with a local BottomSheetModalProvider because iOS modal presentations
  // render in a separate React tree that does not have access to the root-level
  // provider defined in `AppNavigator.tsx`. Placing the provider here ensures
  // the bottom sheet works consistently across platforms.
  return (
    <BottomSheetModalProvider>
      <EditProfileContent
        formData={formData}
        errors={errors}
        onFieldChange={handleFieldChange}
        loading={loading}
        originalEmail={originalEmail}
        isEmailVerified={isEmailVerified}
        onEmailVerify={handleEmailVerify}
      />

      <EmailVerificationSheet
        visible={showEmailVerificationSheet}
        onClose={() => setShowEmailVerificationSheet(false)}
        email={formData.email}
        onVerificationSuccess={handleEmailVerificationSuccess}
      />
    </BottomSheetModalProvider>
  )
})
