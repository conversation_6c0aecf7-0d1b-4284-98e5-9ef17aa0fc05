import { FC, useState, useEffect, useCallback } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { observer } from "mobx-react-lite"
import { BottomSheetScrollView } from "@gorhom/bottom-sheet"
import { BaseBottomSheet, Button, Text } from "@/components"
import { OTPInput } from "@/components/OTPInput"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { SupabaseAuthService } from "@/services/supabase/auth-service"
import { showErrorToast, showSuccessToast } from "@/utils/toast"

interface EmailVerificationSheetProps {
  visible: boolean
  onClose: () => void
  email: string
  onVerificationSuccess: () => void
}

export const EmailVerificationSheet: FC<EmailVerificationSheetProps> = observer(function EmailVerificationSheet({
  visible,
  onClose,
  email,
  onVerificationSuccess,
}) {
  const [code, setCode] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [isResendLoading, setIsResendLoading] = useState(false)
  const [resendCountdown, setResendCountdown] = useState(60) // 1 minute countdown
  const { themed } = useAppTheme()

  // Reset state when sheet opens
  useEffect(() => {
    if (visible) {
      setCode("")
      setIsLoading(false)
      setIsResendLoading(false)
      setResendCountdown(60)
    }
  }, [visible])

  // Countdown timer for resend button
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null

    if (resendCountdown > 0 && visible) {
      interval = setInterval(() => {
        setResendCountdown((prev) => prev - 1)
      }, 1000)
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [resendCountdown, visible])

  const handleVerify = useCallback(async () => {
    if (code.length !== 6) return

    try {
      setIsLoading(true)
      await SupabaseAuthService.verifyEmailChange(email, code)
      showSuccessToast("Email verified successfully!")
      onVerificationSuccess()
      onClose()
    } catch (e: any) {
      console.error("Email verification failed", e)

      // Handle network errors specifically
      if (e?.message?.includes("Network request failed") || e?.name?.includes("AuthRetryableFetchError")) {
        showErrorToast("Network error. Please check your connection and try again.")
      } else if (e?.message?.includes("Invalid") || e?.message?.includes("expired")) {
        showErrorToast("Invalid or expired code. Please try again.")
      } else {
        showErrorToast("Verification failed. Please try again.")
      }
    } finally {
      setIsLoading(false)
    }
  }, [code, email, onVerificationSuccess, onClose])

  const handleResendCode = useCallback(async () => {
    if (resendCountdown > 0 || isResendLoading) return

    try {
      setIsResendLoading(true)
      await SupabaseAuthService.requestEmailChange(email)
      showSuccessToast("Verification code sent!")
      setResendCountdown(60) // Reset countdown to 1 minute
    } catch (e: any) {
      console.error("Resend code failed", e)

      // Handle network errors specifically
      if (e?.message?.includes("Network request failed") || e?.name?.includes("AuthRetryableFetchError")) {
        showErrorToast("Network error. Please check your connection and try again.")
      } else {
        showErrorToast("Failed to resend code. Please try again.")
      }
    } finally {
      setIsResendLoading(false)
    }
  }, [email, resendCountdown, isResendLoading])

  return (
    <BaseBottomSheet
      visible={visible}
      onClose={onClose}
      title="Verify Email"
      snapPoints={["50%"]}
    >
      <BottomSheetScrollView contentContainerStyle={themed($container)}>
        <Text text="Enter the 6-digit code" preset="heading" style={themed($heading)} />
        <Text text={`We've sent it to ${email}`} size="sm" style={themed($subheading)} />

        <OTPInput value={code} onChange={setCode} autoFocus length={6} />

        <View style={themed($spacer)} />

        <Button
          text="Verify"
          onPress={handleVerify}
          disabled={isLoading || code.length !== 6}
          preset="reversed"
        />

        <Text
          text={resendCountdown > 0 ? `Resend code in ${resendCountdown}s` : "Resend code"}
          size="sm"
          weight="light"
          style={[
            themed($resendText),
            themed(resendCountdown > 0 || isResendLoading ? $disabledText : $enabledText)
          ]}
          onPress={resendCountdown > 0 || isResendLoading ? undefined : handleResendCode}
        />
      </BottomSheetScrollView>
    </BaseBottomSheet>
  )
})

const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.lg,
  paddingTop: spacing.md,
  paddingBottom: spacing.xl,
})

const $heading: ThemedStyle<TextStyle> = ({ spacing }) => ({
  marginBottom: spacing.xs,
  textAlign: "center",
})

const $subheading: ThemedStyle<TextStyle> = ({ spacing }) => ({
  marginBottom: spacing.lg,
  textAlign: "center",
})

const $spacer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  height: spacing.lg,
})

const $resendText: ThemedStyle<TextStyle> = ({ spacing }) => ({
  marginTop: spacing.md,
  textAlign: "center",
})

const $enabledText: ThemedStyle<TextStyle> = () => ({
  color: "#007AFF", // Blue color for enabled state
})

const $disabledText: ThemedStyle<TextStyle> = () => ({
  color: "#999999", // Gray color for disabled state
})
