import { observer } from "mobx-react-lite"
import { FC, useState } from "react"
import { View, ViewStyle, TouchableOpacity, TextStyle } from "react-native"
import { Screen, Text, TextField, Button, Icon } from "@/components"
import { AppStackScreenProps } from "@/navigators"
import { useHeader } from "@/utils/useHeader"
import { SupabaseAuthService } from "@/services/supabase/auth-service"
import { showErrorToast, showSuccessToast, showWarningToast } from "@/utils/toast"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { useStores } from "@/models"
import { navigationRef } from "@/navigators"

interface PasswordResetConfirmScreenProps extends AppStackScreenProps<"PasswordResetConfirm"> {}

export const PasswordResetConfirmScreen: FC<PasswordResetConfirmScreenProps> = observer(function PasswordResetConfirmScreen({ navigation }) {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  const { authenticationStore } = useStores()
  const [password, setPassword] = useState("")
  const [loading, setLoading] = useState(false)

  // Handle logout and navigation back to login
  const handleLogoutAndNavigateToLogin = async () => {
    try {
      // Log out from auth store
      authenticationStore.logout()

      // Sign out from Supabase
      const { supabase } = await import('@/supabase')
      await supabase.auth.signOut()

      // Force navigation to Login as a fallback since React Navigation
      // conditional rendering doesn't always work reliably after manual navigation
      setTimeout(() => {
        if (navigationRef.current) {
          navigationRef.current.reset({
            index: 0,
            routes: [{ name: 'Login' as never }],
          })
        }
      }, 200)

    } catch (error) {
      // Force logout even if Supabase fails
      authenticationStore.logout()
    }
  }

  // Header: back arrow that properly handles logout
  useHeader({
    leftContent: (
      <TouchableOpacity onPress={handleLogoutAndNavigateToLogin}>
        <Icon icon="caretLeft" size={24} color={colors.text} />
      </TouchableOpacity>
    ),
    backgroundColor: "transparent",
    includeSafeArea: true,
  })

  // Validation rules
  const hasMinChars = password.length >= 8
  const hasDigit = /\d/.test(password)
  const hasUppercase = /[A-Z]/.test(password)

  const allValid = hasMinChars && hasDigit && hasUppercase

  const handleReset = async () => {
    if (!allValid) {
      showWarningToast("Password must be at least 8 chars, include a digit and uppercase letter.")
      return
    }

    try {
      setLoading(true)
      await SupabaseAuthService.updateUserPassword(password)
      showSuccessToast("Password reset successfully. You can now log in.")

      // Log out and navigate back to login
      await handleLogoutAndNavigateToLogin()

    } catch (error: any) {
      console.error("Password reset failed", error)

      // Handle network errors specifically
      if (error?.message?.includes("Network request failed") || error?.name?.includes("AuthRetryableFetchError")) {
        showErrorToast("Network error. Please check your connection and try again.")
      } else {
        showErrorToast(error?.message ?? "Failed to reset password")
      }
    } finally {
      setLoading(false)
    }
  }

  return (
    <Screen preset="scroll" safeAreaEdges={["bottom"]} contentContainerStyle={themed($container)}>
      {/* Page Heading */}
      <Text text="Reset Password" preset="heading" style={themed($heading)} />
      <Text
        text="Please enter a new password for your account."
        size="sm"
        style={themed($subheading)}
      />

      <TextField
        placeholder="New Password"
        secureTextEntry
        value={password}
        onChangeText={setPassword}
        containerStyle={themed($inputContainer)}
      />

      {/* Validation rules */}
      <View style={themed($rulesContainer)}>
        {[
          { label: "Minimum 8 characters", valid: hasMinChars },
          { label: "At least 1 digit", valid: hasDigit },
          { label: "At least 1 uppercase", valid: hasUppercase },
        ].map((rule, idx) => (
          <View key={idx} style={themed($ruleRow)}>
            <Icon
              icon={rule.valid ? "check" : "x"}
              size={12}
              color={rule.valid ? colors.success ?? "green" : colors.error}
            />
            <Text
              text={rule.label}
              size="sm"
              style={themed([$ruleText, { color: rule.valid ? colors.text : colors.error }])}
            />
          </View>
        ))}
      </View>

      <Button
        text="Reset Password"
        onPress={handleReset}
        disabled={loading}
        preset="filled"
        style={themed($saveButton)}
      />
    </Screen>
  )
})

/* ──────────────────────────────────────────────────────────
 * Styles
 * ─────────────────────────────────────────────────────────*/
const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  padding: spacing.lg,
})

const $heading: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.sm,
})

const $subheading: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.md,
})

const $inputContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.md,
})

const $rulesContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.lg,
})

const $ruleRow: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.xs,
  marginBottom: spacing.xs,
})

const $ruleText: ThemedStyle<TextStyle> = () => ({})

const $saveButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginTop: spacing.lg,
}) 